import { v } from "convex/values";
import { api, internal } from "./_generated/api";
import { internalMutation, mutation, query } from "./_generated/server";
import { canUpdateArticle, generateSlug } from "./helpers/articleHelper";
import { requireUser } from "./users";
import { DOCS_STATUS, ROLES } from "./utils/constants";
import { Id } from "./_generated/dataModel";

// // for columns
export const createColumn = mutation({
  args: {
    title: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      const userId = await requireUser(ctx);
      const user = await ctx.db.get(userId);
      if (!user) {
        return { success: false, error: "Unauthorized." };
      }
      if (user.role !== ROLES.ADMIN) {
        return { success: false, error: "Unauthorized." };
      }
      // get number of columns in board and set order to that number

      const columnId = await ctx.db.insert("taskColumns", {
        ...args,
        createdAt: Date.now(),
      });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "taskColumn",
          docId: columnId,
        },
        docTitle: args.title,
        docStatus: "approved",
        action: "created" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to create column." };
    }
  },
});

export const updateColumn = mutation({
  args: {
    id: v.id("taskColumns"),
    title: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      const userId = await requireUser(ctx);
      const user = await ctx.db.get(userId);
      if (!user) {
        return { success: false, error: "Unauthorized." };
      }
      if (user.role !== ROLES.ADMIN) {
        return { success: false, error: "Unauthorized." };
      }

      await ctx.db.patch(args.id, {
        title: args.title,
        updatedAt: Date.now(),
      });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "taskColumn",
          docId: args.id,
        },
        docTitle: args.title,
        docStatus: "approved",
        action: "updated" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to update column." };
    }
  },
});

export const deleteColumn = mutation({
  args: {
    id: v.id("taskColumns"),
  },
  handler: async (ctx, args) => {
    try {
      const userId = await requireUser(ctx);
      const user = await ctx.db.get(userId);
      if (!user) {
        return { success: false, error: "Unauthorized." };
      }
      if (user.role !== ROLES.ADMIN) {
        return { success: false, error: "Unauthorized." };
      }
      await ctx.db.delete(args.id);
      return { success: true };
    } catch {
      return { success: false, error: "Failed to delete column." };
    }
  },
});

export const createTask = mutation({
  args: {
    columnId: v.id("taskColumns"),
    title: v.string(),
    description: v.optional(v.string()),
    priority: v.union(v.literal("low"), v.literal("medium"), v.literal("high")),
    assignedTo: v.id("users"),
    dueDate: v.optional(v.string()), // store ISO date string (YYYY-MM-DD)
  },
  handler: async (ctx, args) => {
    try {
      const userId = await requireUser(ctx);
      const user = await ctx.db.get(userId);
      if (!user) {
        return { success: false, error: "Unauthorized." };
      }
      if (user.role !== ROLES.ADMIN) {
        return { success: false, error: "Unauthorized." };
      }

      const taskId = await ctx.db.insert("tasks", {
        ...args,
        createdAt: Date.now(),
      });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "task",
          docId: taskId,
        },
        docTitle: args.title,
        docStatus: "approved",
        action: "created" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to create task." };
    }
  },
});

export const updateTask = mutation({
  args: {
    id: v.id("tasks"),
    title: v.string(),
    description: v.optional(v.string()),
    priority: v.union(v.literal("low"), v.literal("medium"), v.literal("high")),
    assignedTo: v.id("users"),
    dueDate: v.optional(v.string()), // store ISO date string (YYYY-MM-DD)
  },
  handler: async (ctx, args) => {
    try {
      const userId = await requireUser(ctx);
      const user = await ctx.db.get(userId);
      if (!user) {
        return { success: false, error: "Unauthorized." };
      }
      if (user.role !== ROLES.ADMIN) {
        return { success: false, error: "Unauthorized." };
      }

      await ctx.db.patch(args.id, {
        ...args,
        updatedAt: Date.now(),
      });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "task",
          docId: args.id,
        },
        docTitle: args.title,
        docStatus: "approved",
        action: "updated" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to update task." };
    }
  },
});

export const deleteTask = mutation({
  args: {
    id: v.id("tasks"),
  },
  handler: async (ctx, args) => {
    try {
      const userId = await requireUser(ctx);
      const user = await ctx.db.get(userId);
      if (!user) {
        return { success: false, error: "Unauthorized." };
      }
      if (user.role !== ROLES.ADMIN) {
        return { success: false, error: "Unauthorized." };
      }
      await ctx.db.delete(args.id);
      return { success: true };
    } catch {
      return { success: false, error: "Failed to delete task." };
    }
  },
});

// get all column in board
export const getAllColumns = query({
  args: {},
  handler: async (ctx) => {
    try {
      const columns = await ctx.db.query("taskColumns").collect();
      if (!columns) return [];
      return columns;
    } catch {
      return { success: false, error: "Failed to fetch columns." };
    }
  },
});

export const getColumn = query({
  args: {
    id: v.id("taskColumns"),
  },
  handler: async (ctx, args) => {
    try {
      const column = await ctx.db.get(args.id);

      return column;
    } catch {
      return { success: false, error: "Failed to fetch column." };
    }
  },
});

export const getAllTasks = query({
  args: {
    columnId: v.id("taskColumns"),
  },
  handler: async (ctx, args) => {
    try {
      const tasks = await ctx.db
        .query("tasks")
        .withIndex("by_columnId", (q) => q.eq("columnId", args.columnId))
        .collect();

      if (!tasks) return [];
      if (tasks.length === 0) return [];
      const tasksWithUsers = await Promise.all(
        tasks.map(async (task) => {
          const user = await ctx.db.get(task.assignedTo);
          return {
            ...task,
            assignee: user?.username,
            assigneeAvatar: user?.imageId
              ? await ctx.storage.getUrl(user?.imageId)
              : user?.image,
          };
        })
      );
      //   remove assignedTo from tasks
      const tasksWithoutAssignedTo = tasksWithUsers.map((task) => {
        const { assignedTo, ...rest } = task;
        return rest;
      });
      return tasksWithoutAssignedTo;
    } catch {
      return { success: false, error: "Failed to fetch tasks." };
    }
  },
});

// export const getBoardData = query(async (ctx, args: { boardId: string }) => {
//   // 1. Get columns for the board
//   const columns = await ctx.db
//     .query("boardColumns")
//     .filter((q) => q.eq(q.field("boardId"), args.boardId))
//     .collect();

//   // 2. Get tasks for this board
//   const tasks = await ctx.db
//     .query("columnTasks")
//     .filter((q) => q.eq(q.field("boardId"), args.boardId))
//     .collect();

//   // based on

//   // 3. Group tasks by columnId
//   // Group by column
//   const grouped: Record<string, any[]> = {};

//   for (const col of columns) {
//     grouped[col._id] = [];
//   }

//   for (const task of tasks) {
//     let assigneeName = "Unassigned";
//     let assigneeAvatar = "";

//     if (task.assignedTo) {
//       const user = await ctx.db.get(task.assignedTo);
//       if (user) {
//         assigneeName = user.name ?? "Unknown User";
//         assigneeAvatar = user.image ?? "";
//       }
//     }

//     const col = columns.find((c) => c._id === task.columnId);

//     if (col) {
//       grouped[col._id].push({
//         id: task._id,
//         title: task.title,
//         description: task.description,
//         priority: task.priority,
//         assignee: assigneeName,
//         assigneeAvatar: assigneeAvatar,
//         dueDate: task.dueDate,
//       });
//     }
//   }
//   return grouped;
// });

// export const getBoardDatas = query({
//   args: {
//     boardId: v.id("boards"),
//   },
//   handler: async (ctx, args) => {
//     // check if that id exist
//     const board = await ctx.db.get(args.boardId);
//     if (!board) {
//       return { success: false, error: "Board not found." };
//     }

//     // 1. Get columns for the board
//     const columns = await ctx.db
//       .query("boardColumns")
//       .filter((q) => q.eq(q.field("boardId"), args.boardId))
//       .collect();

//     // 2. Get tasks for this board
//     const tasks = await ctx.db
//       .query("columnTasks")
//       .filter((q) => q.eq(q.field("boardId"), args.boardId))
//       .collect();

//     // 3. Combine columns and tasks
//     const formattedColumns = columns.map((col) => ({
//       colId: col._id,
//       colName: col.title,
//       tasks: tasks
//         .filter((t) => t.columnId === col._id)
//         .map((t) => ({
//           id: t._id,
//           title: t.title,
//           description: t.description,
//           priority: t.priority,
//           assignee: t.assignedTo,
//           dueDate: t.dueDate,
//         })),
//     }));

//     // 4. Return board data

//     return {
//       boardId: board._id,
//       boardTitle: board.name,
//       columns: formattedColumns,
//     };
//   },
// });

// // change column in which task is in
// export const moveTask = mutation({
//   args: {
//     taskId: v.id("columnTasks"),
//     newColumnId: v.id("boardColumns"),
//   },
//   handler: async (ctx, args) => {
//     try {
//       const userId = await requireUser(ctx);
//       const user = await ctx.db.get(userId);
//       if (!user) {
//         return { success: false, error: "Unauthorized." };
//       }
//       if (user.role !== ROLES.ADMIN) {
//         return { success: false, error: "Unauthorized." };
//       }
//       await ctx.db.patch(args.taskId, {
//         columnId: args.newColumnId,
//       });
//       return { success: true };
//     } catch {
//       return { success: false, error: "Failed to move task." };
//     }
//   },
// });
