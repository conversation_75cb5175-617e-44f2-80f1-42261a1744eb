'use client';
import { DialogDescription } from '@ariakit/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from 'convex/react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Spinner } from '@/components/custom/spinner';
import { Button, buttonVariants } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import { cn } from '@/lib/utils';
import { type TTaskSchema, taskSchema } from './schema';

interface CreateOrUpdateTaskProps {
  openDialog?: boolean;
  setOpenDialog?: (open: boolean) => void;
  title?: string;
  description?: string;
  priority?: 'low' | 'medium' | 'high';
  assignedTo?: Id<'users'>;
  dueDate?: string;
  id?: Id<'tasks'>;
  columnId: Id<'taskColumns'>;
}

export default function CreateOrUpdateTask({
  openDialog,
  setOpenDialog,
  title,
  description,
  priority,
  assignedTo,
  dueDate,
  id,
  columnId,
}: CreateOrUpdateTaskProps) {
  const isEditMode = Boolean(id);
  const createTaskMutation = useMutation(api.board.createTask);
  const updateTaskMutation = useMutation(api.board.updateTask);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [internalOpen, setInternalOpen] = useState(false);

  const open = openDialog ?? internalOpen;
  const setOpen = setOpenDialog ?? setInternalOpen;

  const form = useForm<TTaskSchema>({
    resolver: zodResolver(taskSchema),
    defaultValues: {
      title: title || '',
      description: description || '',
      priority: priority || 'low',
      assignedTo: assignedTo || '',
      dueDate: dueDate || '',
    },
  });
  useEffect(() => {
    if (open) {
      form.reset({
        title: title || '',
        description: description || '',
        priority: priority || 'low',
        assignedTo: assignedTo || '',
        dueDate: dueDate || '',
      });
    }
  }, [open, title, description, priority, assignedTo, dueDate, form]);

  const handleSubmit = async (data: TTaskSchema) => {
    setIsSubmitting(true);
    setOpen(false);

    try {
      if (isEditMode) {
        if (!id) {
          toast.error('Failed to get task id.');
          return;
        }

        const result = await updateTaskMutation({
          id,
          title: data.title,
          description: data.description,
          priority: data.priority,
          dueDate: data.dueDate,
        });
        if (result?.success) {
          form.reset();
          toast.success('Task updated successfully!');
        } else {
          toast.error(result.error);
        }
      } else {
        const result = await createTaskMutation({
          columnId,
          title: data.title,
          description: data.description,
          priority: data.priority,
          dueDate: data.dueDate,
        });
        if (result?.success) {
          form.reset();
          toast.success('Task created successfully!');
        } else {
          toast.error(result.error);
        }
      }
    } catch {
      toast.error('Something went wrong.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog onOpenChange={setOpen} open={open}>
      {!isEditMode && (
        <DialogTrigger
          className={cn(
            buttonVariants({ variant: 'default', size: 'lg' }),
            'cursor-pointer rounded-sm px-3'
          )}
        >
          New Task
        </DialogTrigger>
      )}

      <DialogContent className="flex w-full max-w-md flex-col">
        <DialogHeader className="sr-only">
          <DialogTitle>{isEditMode ? 'Update' : 'Create New'} Task</DialogTitle>
          <DialogDescription>
            {isEditMode
              ? 'Update your task to this column.'
              : 'Add a new task to this column.'}{' '}
            Task
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            className="mt-6 space-y-4"
            onSubmit={form.handleSubmit(handleSubmit)}
          >
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input placeholder="Title" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button className="w-full" disabled={isSubmitting} type="submit">
              {isSubmitting ? (
                <Spinner text="Submitting..." />
              ) : isEditMode ? (
                'Update Task'
              ) : (
                'Create Task'
              )}
            </Button>
          </form>
        </Form>
        <DialogClose />
      </DialogContent>
    </Dialog>
  );
}
